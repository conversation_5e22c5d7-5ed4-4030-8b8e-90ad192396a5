import os
import allure
import pytest
import json
from pytest import assume
from ..hui_api.user_init import UserInit
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager

"""测试用户初始化"""

@allure.epic('用户初始化')
@allure.feature('用户初始化')
class TestUserInit():
    @pytest.mark.order(3)  # 添加顺序标记，数字越小优先级越高
    # @allure.severity(allure.severity_level.CRITICAL)  # 设置测试用例的严重程度
    @allure.story('用户初始化')
    @allure.title('用户初始化成功')
    def test_user_init(self):
        with allure.step("用户初始化"):
            
            
            # 创建用户初始化实例
            user_init = UserInit()
           
            resp = user_init.send_request()
        
            
            # 解析json响应-需要抽离出独立方法-待处理
            try:
                resp_json = resp.json()
                print(f"响应内容: {resp_json}")
            except json.JSONDecodeError:
                print(f"响应不是有效的 JSON 格式: {resp.text}")
                # 断言失败，并提供详细信息
                pytest.fail(f"响应不是有效的 JSON 格式。状态码: {resp.status_code}, 响应内容: {resp.text}")
            
            assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')


            # 断言cid成功提取
            assume(user_init.cid is not None, "cid提取失败，值为None")
            # 断言uid成功提取
            assume(user_init.uid is not None, "uid提取失败，值为None")

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件