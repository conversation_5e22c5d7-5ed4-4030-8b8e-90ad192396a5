import os
import allure
import pytest
from pytest import assume
from ..hui_api.customer_online import CustomerOnline
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager

"""测试在线客服工作台-在线状态-在线 """

@allure.epic('在线客服工作台-在线状态-在线')
@allure.feature('在线客服工作台-在线状态-在线')
class Test_CustomerOnline():
    @pytest.mark.order(2)  # 添加顺序标记，数字越小优先级越高
    @allure.story('在线状态-在线')
    @allure.title('在线状态-在线-成功')
    def test_user_init(self):
        with allure.step("在线状态-在线"):
            # 从配置文件加载ali环境的配置
            env_config = EnvManager.get_env_config('ali')
            # 设置基础配置信息
            OnlineBaseSaasApi.configInfo = env_config
            
            # 创建用户初始化实例
            user_init = CustomerOnline()
            resp = user_init.send_request()
            print(f"\n响应状态码: {resp.status_code}")
            print(f"响应头: {dict(resp.headers)}")
            print(f"响应内容: {resp.text}")
            print(f"响应编码: {resp.encoding}")
            
            assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')
            if resp.text:  # 只有在响应内容不为空时才尝试解析JSON
                ret_code = resp.json()['retCode']
                assume(ret_code == '000000', f'ret_code不对，预期:000000,实际:{ret_code}')
                print("=============响应结果==============", resp.json())

if __name__ == '__main__':
   
    
    # 运行测试
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件