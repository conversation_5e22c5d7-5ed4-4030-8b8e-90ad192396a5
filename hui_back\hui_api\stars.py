"""
在线工作台-标星
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class Stars(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['stars_url'] + env_config['stars_path']
        else:
            self.url = env_config['url'] + env_config['path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/x-www-form-urlencoded'
            # 'content-type': 'application/json'
        }

        # 移除json属性，避免混淆
        self.json = None

        # data 将在 send_request 时动态设置
        self.data = None

    def send_request(self, **kwargs):
        """
        重写 send_request 方法，在发送请求前动态设置 uid 和 cid
        """
        # 获取配置
        env_config = OnlineBaseSaasApi.configInfo

        # 确保从变量管理器获取已提取的 uid 和 cid
        extracted_uid = self.ensure_global_param('uid')
        extracted_cid = self.ensure_global_param('cid')

        # 如果提取的值是默认值，尝试从配置获取
        if str(extracted_uid).startswith('default_'):
            extracted_uid = env_config.get('uid', '')
        if str(extracted_cid).startswith('default_'):
            extracted_cid = env_config.get('cid', '')

        print(f"[Stars] 动态获取的 uid: {extracted_uid}")
        print(f"[Stars] 动态获取的 cid: {extracted_cid}")

        # 动态设置请求参数
        self.data = {
            'sender': extracted_uid,  # 使用确保的 uid 值
            'receiver': extracted_cid  # 使用确保的 cid 值
        }

        # 打印请求数据
        print(f"[Stars] 动态设置请求数据: {self.data}")

        # 调用父类的 send_request 方法
        return super().send_request(**kwargs)
        

       
