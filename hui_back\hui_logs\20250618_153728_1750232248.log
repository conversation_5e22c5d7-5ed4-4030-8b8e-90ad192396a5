[2025-06-18 15:37:28,583] [INFO] [pytest_result_log] - Start: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init
[2025-06-18 15:37:28,738] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-18 15:37:28,740] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-18 15:37:28,741] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-18 15:37:28,741] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-18 15:37:28,741] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 15:37:28,742] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 15:37:28,742] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:37:28,742] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:37:28,747] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 15:37:28,747] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 15:37:28,747] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 15:37:28,747] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 15:37:28,747] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 15:37:28,747] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 15:37:28,748] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 15:37:28,748] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 15:37:28,749] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 15:37:28,749] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 15:37:28,749] [DEBUG] [root] - 额外参数: {}
[2025-06-18 15:37:28,749] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 15:37:28,749] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:37:28,749] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 15:37:28,749] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 15:37:28,750] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:37:28,750] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 15:37:29,409] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 15:37:29,409] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.evWB9lCLS-_T2xnt_kSRhd0ltfgJSX1O74NJkCQHFto","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 15:37:29,409] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 15:37:29,409] [INFO] [root] - 【API响应】请求完成，耗时: 0.66秒，状态码: 200
[2025-06-18 15:37:29,409] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 15:37:29,410] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 15:37:29,410] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 15:37:29,410] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:37:29,410] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 15:37:29,410] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:37:29,411] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-18 15:37:29,417] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:29,417] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:29,417] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-kwb/admin/online.action
[2025-06-18 15:37:29,418] [INFO] [apitest] - 请求参数 json：{'uid': 'xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj+IBS1zqXJr8fZ4ny5vE8t'}
[2025-06-18 15:37:30,248] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:30,249] [INFO] [apitest] - 响应体：
[2025-06-18 15:37:30,250] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:30,251] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init): 
[2025-06-18 15:37:30,252] [INFO] [pytest_result_log] - End: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init-
[2025-06-18 15:37:30,255] [INFO] [pytest_result_log] - Start: hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest
[2025-06-18 15:37:30,258] [INFO] [hui_back.hui_testcases.test_login] - 成功获取会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:37:30,259] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest): 
[2025-06-18 15:37:30,261] [INFO] [pytest_result_log] - End: hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest
[2025-06-18 15:37:30,264] [INFO] [pytest_result_log] - Start: hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary
[2025-06-18 15:37:30,266] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:30,267] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:30,267] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:30,268] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750232242_1429a87f&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750232242_1429a87f
[2025-06-18 15:37:31,737] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:31,738] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"c47e6194d7cd4959ba959cefdc3edd6e:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:31,741] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:31,743] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:31,746] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:31,749] [INFO] [apitest] - 通过【$.cid】提取到的结果是:cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:31,750] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:31,751] [INFO] [apitest] - 将提取的值【cbebe889a9dd4f6db9f7e2110317f6d2】保存为变量【cid】
[2025-06-18 15:37:31,752] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:31,754] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:31,756] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-18 15:37:31,757] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:31,760] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-18 15:37:31,761] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:31,762] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:31,763] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:31,763] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:32,947] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:32,947] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"08e7bc80fe404de793e0959b8c6a2955:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:32,952] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:32,954] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:32,956] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:32,957] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/conversation/summarySubmitVer2.action
[2025-06-18 15:37:32,959] [INFO] [apitest] - 请求参数 data：updateServiceId=&uid=56de2146104f480d8418d5400c978305&cid=cbebe889a9dd4f6db9f7e2110317f6d2&invalidSession=1
[2025-06-18 15:37:34,092] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:34,093] [INFO] [apitest] - 响应体：{"status":0}
[2025-06-18 15:37:34,094] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:34,095] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary): 
[2025-06-18 15:37:34,096] [INFO] [pytest_result_log] - End: hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary
[2025-06-18 15:37:34,099] [INFO] [pytest_result_log] - -----------Start: hui_testcases/test_stars.py::Test_Stars::test_stars-----------
[2025-06-18 15:37:34,100] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:34,102] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:34,103] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/admin/add_marklist.action
[2025-06-18 15:37:34,104] [INFO] [apitest] - 请求参数 data：sender=56de2146104f480d8418d5400c978305&receiver=cbebe889a9dd4f6db9f7e2110317f6d2&uid=56de2146104f480d8418d5400c978305&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:35,262] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:35,263] [INFO] [apitest] - 响应体：
[2025-06-18 15:37:35,263] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:35,265] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_stars.py::Test_Stars::test_stars): 
[2025-06-18 15:37:35,266] [INFO] [pytest_result_log] - ------------End: hui_testcases/test_stars.py::Test_Stars::test_stars------------
[2025-06-18 15:37:35,268] [INFO] [pytest_result_log] - --Start: hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human---
[2025-06-18 15:37:35,271] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:35,271] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:35,272] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:35,273] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:36,466] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:36,467] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"c168bf7a27d84a4bb9792787f45065fa:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:36,470] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:36,471] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:36,474] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:36,479] [INFO] [apitest] - 通过【$.cid】提取到的结果是:cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:36,481] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:36,481] [INFO] [apitest] - 将提取的值【cbebe889a9dd4f6db9f7e2110317f6d2】保存为变量【cid】
[2025-06-18 15:37:36,482] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:36,486] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:36,494] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-18 15:37:36,495] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:36,495] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-18 15:37:36,497] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:36,497] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:36,498] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:36,498] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:37,860] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:37,860] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"18132960cdd543ada04c7f3cac368da5:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:37,862] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:37,863] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:37,863] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:37,864] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-web/user/chatconnect.action
[2025-06-18 15:37:37,865] [INFO] [apitest] - 请求参数 data：sysNum=bdbe5660b2eb45758739527fda974091&chooseAdminId=&tranFlag=0&current=false&groupId=&transferType=0&summaryParams=&transferAction=&flowType=&flowCompanyId=&flowGroupId=&activeTransfer=1&unknownQuestion=&docId=&adminHelloWord=null&uid=56de2146104f480d8418d5400c978305&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:39,025] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:39,026] [INFO] [apitest] - 响应体：{"adminHelloWord":"您好，请问有什么可以帮您的？","puid":"f4459c131c55452d88e124fe4f9cb1cd","pu":"/webchat","queueFlag":null,"aname":"api","logTraceId":"b6068c3a3df6496b8c586b6699a3f3a9_","aface":"https://img.sobot.com/console/common/face/admin.png","wslink.bak":["wss://imwsten.sobot.com:9001/ws","wss://imwsten.sobot.com:9002/ws","wss://imwsten.sobot.com:9003/ws","wss://imwsten.sobot.com:9004/ws","wss://imwsten.sobot.com:9005/ws","wss://imwsten.sobot.com:9006/ws"],"serviceEndPushMsg":"55","aid":"83d4039186d8452b88a59873bfcf2084","wslink.default":"wss://imwsten.sobot.com:9004/ws","status":1}
[2025-06-18 15:37:39,026] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:39,027] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human): 
[2025-06-18 15:37:39,028] [INFO] [pytest_result_log] - ---End: hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human----
[2025-06-18 15:37:39,029] [INFO] [pytest_result_log] - ------Start: hui_testcases/test_user_init.py::TestUserInit::test_user_init------
[2025-06-18 15:37:39,030] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:39,031] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:39,031] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:39,031] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:40,393] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:40,393] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"d0ca12c828894a49a62446cf94dfd392:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"f4459c131c55452d88e124fe4f9cb1cd","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:40,395] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:40,396] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:40,397] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:40,398] [INFO] [apitest] - 通过【$.cid】提取到的结果是:cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:40,399] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:40,399] [INFO] [apitest] - 将提取的值【cbebe889a9dd4f6db9f7e2110317f6d2】保存为变量【cid】
[2025-06-18 15:37:40,399] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:40,402] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:37:40,403] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-18 15:37:40,403] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:40,404] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-18 15:37:40,405] [INFO] [apitest] - #######################################################################
[2025-06-18 15:37:40,405] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:37:40,406] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:37:40,406] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:37:41,549] [INFO] [apitest] - 响应码：200
[2025-06-18 15:37:41,549] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"e63ca2a3cf7148bea491987a31c4b74f:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"f4459c131c55452d88e124fe4f9cb1cd","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:37:41,551] [INFO] [apitest] - #######################################################################


[2025-06-18 15:37:41,552] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_user_init.py::TestUserInit::test_user_init): 
[2025-06-18 15:37:41,554] [INFO] [pytest_result_log] - -------End: hui_testcases/test_user_init.py::TestUserInit::test_user_init-------
[2025-06-18 15:37:41,554] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
