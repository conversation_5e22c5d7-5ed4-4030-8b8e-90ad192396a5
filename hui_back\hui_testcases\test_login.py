#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文件 - 用于验证登录功能是否正常
"""
import pytest
import allure
import logging
from pytest import assume
from ..hui_common.v6_console_login import V6ConsoleLogin

logger = logging.getLogger(__name__)

@allure.epic('登录功能验证')
@allure.feature('控制台登录')
class TestLoginFunctionality:
    @pytest.mark.order(1)  # 添加顺序标记，数字越小优先级越高
    @allure.story('验证登录功能')
    @allure.title('验证conftest中的自动登录功能')
    def test_login_from_conftest(self, session_id):
        """
        测试conftest.py中的登录功能
        使用session_id夹具验证是否已成功登录
        """
        assume(session_id is not None and len(session_id) > 0, "登录失败，未获取到有效的会话ID")
        logger.info(f"成功获取会话ID: {session_id[:15]}...")
        print(f"\n成功获取会话ID: {session_id[:15]}...")
        
    @allure.story('验证手动登录')
    @allure.title('手动调用登录接口')
    def test_manual_login(self):
        """
        测试手动调用登录接口
        """
        try:
            # 获取可用环境
            envs = V6ConsoleLogin.get_available_envs()
            assume(len(envs) > 0, "未找到可用的环境配置")

            # 默认使用ali环境
            env_name = 'ali'
            assume(env_name in envs, f"环境{env_name}未在配置文件中找到")

            # 执行登录
            resp, session_id = V6ConsoleLogin.login(env_name)

            # 验证响应
            assume(resp is not None, "登录请求失败，响应为空")
            assume(resp.status_code == 200, f"登录失败，状态码: {resp.status_code}")

            # 验证会话ID
            assume(session_id is not None and len(session_id) > 0, "登录成功但未获取到有效的会话ID")
            
            logger.info(f"手动登录成功，会话ID: {session_id[:15]}...")
            print(f"\n手动登录成功，会话ID: {session_id[:15]}...")
            
        except Exception as e:
            logger.error(f"登录测试失败: {str(e)}")
            pytest.fail(f"登录测试失败: {str(e)}") 