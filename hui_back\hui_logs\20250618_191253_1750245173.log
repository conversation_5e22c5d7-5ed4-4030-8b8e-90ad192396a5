[2025-06-18 19:12:53,368] [INFO] [pytest_result_log] - ------Start: hui_testcases/test_user_init.py::TestUserInit::test_user_init------
[2025-06-18 19:12:53,486] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-18 19:12:53,488] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-18 19:12:53,488] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-18 19:12:53,489] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-18 19:12:53,490] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 19:12:53,490] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 19:12:53,490] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 19:12:53,490] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 19:12:53,494] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 19:12:53,495] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 19:12:53,495] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 19:12:53,496] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 19:12:53,496] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 19:12:53,497] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 19:12:53,497] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 19:12:53,497] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 19:12:53,498] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 19:12:53,498] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 19:12:53,498] [DEBUG] [root] - 额外参数: {}
[2025-06-18 19:12:53,498] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 19:12:53,498] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 19:12:53,499] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 19:12:53,499] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 19:12:53,499] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 19:12:53,500] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 19:12:54,189] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 19:12:54,189] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.xODPUdEI_apZp2mHnuhc6KyO4peOw7HErmYCX6cOrtM","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 19:12:54,189] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 19:12:54,190] [INFO] [root] - 【API响应】请求完成，耗时: 0.69秒，状态码: 200
[2025-06-18 19:12:54,191] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 19:12:54,191] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 19:12:54,191] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 19:12:54,191] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 19:12:54,192] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 19:12:54,192] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 19:12:54,193] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-18 19:12:54,194] [INFO] [apitest] - #######################################################################
[2025-06-18 19:12:54,195] [INFO] [apitest] - 请求方法：post
[2025-06-18 19:12:54,196] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 19:12:54,196] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750245173_a7c3c0d0&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750245173_a7c3c0d0
