from api.saas.chatbot.public_data.entity_management import SearchKeywodEntityCheckApi
from api.saas.chatbot.public_data.synonym_api import SearchSynonymDocApi
from api.saas.chatbot.public_data.terms_api import AddTermsApi, GetTermsApi
from api.saas.chatbot.public_data.variable_management_api import SearchVariableApi
from data.Saas.chatbot.public_data import add_terms_param, get_terms_param, get_synonym_param, get_variable_param, \
    search_keyword_entity_check_param


# region 专业名词
def add_terms_action(prv_word_content=None, word_type=3):
    """
    新增专业名词
    prv_word_content:专业名词内容
    word_type：固定的，不要改
    """
    param = add_terms_param(prv_word_content, word_type)
    AddTermsApi(**param).send_request().json()
    return get_all_terms_action(param["prv_word_content"])


# 递归循环获取专业名词
def get_all_terms_action(words=None, word_type=3, page_no=1, page_size=100):
    param = get_terms_param(words, word_type, page_no, page_size)
    resp = GetTermsApi(**param).send_request().json()
    items = resp["items"]
    if resp['totalCount'] is None:
        return items
    if resp["totalCount"] > page_size and page_no * page_size < resp["totalCount"]:
        items += get_all_terms_action(words, word_type, page_no + 1, page_size)
    return items


# endregion

# region 同义词

# 递归获取同义词
def get_all_synonym_action(words=None, word_type=2, page_no=1, page_size=100):
    param = get_synonym_param(words, word_type, page_no, page_size)
    resp = SearchSynonymDocApi(**param).send_request().json()
    items = resp["items"]
    if resp['totalCount'] is None:
        return items
    if resp["totalCount"] > page_size and page_no * page_size < resp["totalCount"]:
        items += get_all_synonym_action(words, word_type, page_no + 1, page_size)
    return items


# endregion

# region 变量
def get_all_variable_action(classify_id=-1, page_no=1, page_size=100, query_name=None, business_line=0):
    param = get_variable_param(classify_id, page_no, page_size, query_name, business_line)
    resp = SearchVariableApi(**param).send_request().json()
    items = resp["items"]
    if resp['totalCount'] is None:
        return items
    if resp["totalCount"] > page_size and page_no * page_size < resp["totalCount"]:
        items += get_all_variable_action(query_name, page_no + 1, page_size, classify_id, business_line)
    return items


# endregion

# region 实体管理
def get_all_keyword_entity_check_action(key_word="", page_no=1, page_size=100, order_type=2, type=1):
    """
    获取关键词实体
    """
    param = search_keyword_entity_check_param(key_word, page_no, page_size, order_type, type)
    resp = SearchKeywodEntityCheckApi(**param).send_request().json()
    items = resp["items"]
    if resp['totalCount'] is None:
        return items
    if resp["totalCount"] > page_size and page_no * page_size < resp["totalCount"] :
        items += get_all_keyword_entity_check_action(key_word, page_no + 1, page_size, order_type, type)
    return items
# endregion
