"""
在线工作台-标星
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class Stars(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['stars_url'] + env_config['stars_path']
        else:
            self.url = env_config['url'] + env_config['path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/x-www-form-urlencoded'
            # 'content-type': 'application/json'
        }

        # 移除json属性，避免混淆
        self.json = None

        # 设置初始请求参数，使用变量引用
        self.data = {
            'sender': '${uid}',  # 使用变量引用，自动替换
            'receiver': '${cid}'  # 使用变量引用，自动替换
        }

    def send_request(self, **kwargs):
        """
        重写 send_request 方法，在发送请求前确保使用最新的 uid 和 cid
        """
        # 在发送请求前，强制刷新 data 中的 uid 和 cid 参数
        extracted_params = self.refresh_data_with_extracted_params('uid', 'cid')

        # 更新 data 中的字段名（sender 对应 uid，receiver 对应 cid）
        self.data['sender'] = extracted_params.get('uid', '')
        self.data['receiver'] = extracted_params.get('cid', '')

        # 打印调试信息
        print(f"[Stars] 刷新后的请求数据: {self.data}")

        # 调用父类的 send_request 方法
        return super().send_request(**kwargs)
        

       
