[2025-06-18 19:16:09,629] [INFO] [pytest_result_log] - -------------Start: verify_assume_behavior.py::test_assert_behavior-------------
[2025-06-18 19:16:09,945] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-18 19:16:09,946] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-18 19:16:09,947] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-18 19:16:09,949] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-18 19:16:09,949] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 19:16:09,951] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 19:16:09,951] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 19:16:09,952] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 19:16:09,963] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 19:16:09,965] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 19:16:09,965] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 19:16:09,967] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 19:16:09,967] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 19:16:09,967] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 19:16:09,968] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 19:16:09,968] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 19:16:09,969] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 19:16:09,970] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 19:16:09,970] [DEBUG] [root] - 额外参数: {}
[2025-06-18 19:16:09,970] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 19:16:09,970] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 19:16:09,971] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 19:16:09,976] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 19:16:09,977] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 19:16:09,979] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 19:16:11,123] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 19:16:11,178] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.f1wWV3WFxGBPjYQ9HrItgL-jFd90c3Hxce5pb9TRLEQ","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 19:16:11,180] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 19:16:11,181] [INFO] [root] - 【API响应】请求完成，耗时: 1.21秒，状态码: 200
[2025-06-18 19:16:11,182] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 19:16:11,188] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 19:16:11,191] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 19:16:11,193] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 19:16:11,195] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 19:16:11,197] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 19:16:11,199] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-18 19:16:11,234] [INFO] [pytest_result_log] - test status is PASSED (verify_assume_behavior.py::test_assert_behavior): 
[2025-06-18 19:16:11,243] [INFO] [pytest_result_log] - --------------End: verify_assume_behavior.py::test_assert_behavior--------------
[2025-06-18 19:16:11,260] [INFO] [pytest_result_log] - -------------Start: verify_assume_behavior.py::test_assume_behavior-------------
[2025-06-18 19:16:13,113] [ERROR] [pytest_result_log] - test status is FAILED (verify_assume_behavior.py::test_assume_behavior): FailedAssumption
[2025-06-18 19:16:13,114] [DEBUG] [pytest_result_log] - verify_assume_behavior.py::test_assume_behavior -> tp = <class 'pytest_assume.plugin.FailedAssumption'>, value = None, tb = None

    def reraise(tp, value, tb=None):
        try:
            if value is None:
                value = tp()
            if value.__traceback__ is not tb:
>               raise value.with_traceback(tb)
E               pytest_assume.plugin.FailedAssumption: 
E               2 Failed Assumptions:
E               
E               verify_assume_behavior.py:33: AssumptionFailure
E               >>	assume(2 == 3, "第二个断言：2 == 3 (应该失败)")
E               AssertionError: 第二个断言：2 == 3 (应该失败)
E               assert False
E               
E               verify_assume_behavior.py:39: AssumptionFailure
E               >>	assume(4 == 5, "第四个断言：4 == 5 (应该失败)")
E               AssertionError: 第四个断言：4 == 5 (应该失败)
E               assert False

..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\six.py:718: FailedAssumption
[2025-06-18 19:16:13,115] [INFO] [pytest_result_log] - --------------End: verify_assume_behavior.py::test_assume_behavior--------------
[2025-06-18 19:16:13,117] [INFO] [pytest_result_log] - -------------Start: verify_assume_behavior.py::test_mixed_behavior--------------
[2025-06-18 19:16:13,304] [ERROR] [pytest_result_log] - test status is FAILED (verify_assume_behavior.py::test_mixed_behavior): FailedAssumption
[2025-06-18 19:16:13,305] [DEBUG] [pytest_result_log] - verify_assume_behavior.py::test_mixed_behavior -> tp = <class 'pytest_assume.plugin.FailedAssumption'>, value = None, tb = None

    def reraise(tp, value, tb=None):
        try:
            if value is None:
                value = tp()
            if value.__traceback__ is not tb:
>               raise value.with_traceback(tb)
E               pytest_assume.plugin.FailedAssumption: 
E               1 Failed Assumptions:
E               
E               verify_assume_behavior.py:49: AssumptionFailure
E               >>	assume(2 == 3, "assume: 2 == 3 (失败)")
E               AssertionError: assume: 2 == 3 (失败)
E               assert False

..\..\..\AppData\Local\Programs\Python\Python311\Lib\site-packages\six.py:718: FailedAssumption
[2025-06-18 19:16:13,307] [INFO] [pytest_result_log] - --------------End: verify_assume_behavior.py::test_mixed_behavior---------------
[2025-06-18 19:16:13,308] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
