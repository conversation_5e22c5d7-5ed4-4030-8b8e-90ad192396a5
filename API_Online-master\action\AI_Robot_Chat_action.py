# 访客端进线与机器人会话
from json import JSONDecodeError

from action.在线客服工作台与访客端的执行动作 import *

question_list =customer_segmentation_questions = [
    # 行为类
    "如何根据客户的购买频率和金额进行细分？",
    "如何根据客户的购买金额波动进行细分？",
    "如何根据客户的购买频率变化进行细分？",
    "如何根据客户的购买时间间隔进行细分？",
    "如何根据客户的购买时间段（早、中、晚）进行细分？",
    "如何根据客户的购买地点分布进行细分？",
    "如何根据客户的购买渠道（线上、线下）进行细分？",
    "如何根据客户对促销活动的响应进行细分？",
    "如何根据客户对不同促销活动的偏好进行细分？",
    "如何根据客户对折扣促销的敏感度进行细分？",
    "如何根据客户对赠品促销的敏感度进行细分？",
    "如何根据客户对限时活动的响应进行细分？",
    "如何根据客户对新产品接受程度进行细分？",
    "如何根据客户对关联产品的购买行为进行细分？",
    "如何根据客户购买的产品组合进行细分？",
    "如何根据客户购买的季节性特征进行细分？",
    "如何根据客户购买的周期性特征进行细分？",
    "如何根据客户购买的支付方式（信用卡、支付宝等）进行细分？",
    "如何根据客户购买的设备类型（手机、电脑等）进行细分？",
    "如何根据客户购买的退换货频率进行细分？",
    "如何根据客户购买的投诉记录进行细分？",
    "如何根据客户购买的时间趋势进行细分？",
    "如何根据客户在网站上的浏览行为进行细分？",
    "如何根据客户在网站上的停留时间进行细分？",
    "如何根据客户在网站上的点击路径进行细分？",

    # 特征类
    "如何根据客户的年龄分层（如 18-25 岁、26-35 岁等）进行细分？",
    "如何根据客户的性别进行细分？",
    "如何根据客户的收入水平进行细分？",
    "如何根据客户的消费能力（高、中、低）进行细分？",
    "如何根据客户的教育程度进行细分？",
    "如何根据客户的家庭状况（单身、已婚、有子女等）进行细分？",
    "如何根据客户的地理位置（城市、乡村、地区）进行细分？",
    "如何根据客户的兴趣爱好进行细分？",
    "如何根据客户的信用评分进行细分？",
    "如何根据客户的生命周期价值（LTV）进行细分？",
    "如何根据客户对品牌的忠诚度进行细分？",
    "如何根据客户对品牌的忠诚度变化进行细分？",
    "如何根据客户对品牌的认知度进行细分？",
    "如何根据客户的兴趣偏好进行细分？",
    "如何根据客户对服务的满意度进行细分？",

    # 偏好类
    "如何根据客户对不同产品的偏好进行细分？",
    "如何根据客户对不同产品类别的偏好进行细分？",
    "如何根据客户对不同品牌产品的偏好进行细分？",
    "如何根据客户对不同价格区间产品的偏好进行细分？",
    "如何根据客户对不同功能产品的偏好进行细分？",
    "如何根据客户对不同包装产品的偏好进行细分？",
    "如何根据客户对不同颜色产品的偏好进行细分？",
    "如何根据客户对不同材质产品的偏好进行细分？",
    "如何根据客户对不同口味产品的偏好进行细分？",
    "如何根据客户对不同服务类型的偏好进行细分？",

    # 动态类
    "如何根据客户对促销活动的参与频率进行细分？",
    "如何根据客户对促销活动的参与时间进行细分？",
    "如何根据客户对促销活动的参与金额进行细分？",
    "如何根据客户对促销活动的参与深度进行细分？",
    "如何根据客户在社交媒体上的活跃度进行细分？",
    "如何根据客户在社交媒体上的互动频率进行细分？",
    "如何根据客户在社交媒体上的分享行为进行细分？",
    "如何根据客户在社交媒体上的评论行为进行细分？",
    "如何根据客户在社交媒体上的点赞行为进行细分？",
    "如何根据客户在社交媒体上的关注行为进行细分？",

    # 趋势类
    "如何根据客户购买金额的增长趋势进行细分？",
    "如何根据客户购买频率的增长趋势进行细分？",
    "如何根据客户购买时间间隔的变化趋势进行细分？",
    "如何根据客户对促销活动的响应趋势进行细分？",
    "如何根据客户对新产品接受的快慢进行细分？",
    "如何根据客户对品牌的忠诚度变化趋势进行细分？",
    "如何根据客户对服务的满意度变化趋势进行细分？",
    "如何根据客户对价格的敏感度变化趋势进行细分？",
    "如何根据客户对促销活动的敏感度变化趋势进行细分？",
    "如何根据客户对不同营销渠道的响应趋势进行细分？",

    # 组合类
    "如何根据客户的年龄和购买频率进行细分？",
    "如何根据客户的收入水平和购买金额进行细分？",
    "如何根据客户的地理位置和购买行为进行细分？",
    "如何根据客户的兴趣爱好和购买偏好进行细分？",
    "如何根据客户的家庭状况和消费能力进行细分？",
    "如何根据客户的教育程度和购买行为进行细分？",
    "如何根据客户的信用评分和购买金额进行细分？",
    "如何根据客户的忠诚度和购买频率进行细分？",
    "如何根据客户的生命周期价值和购买金额进行细分？",
    "如何根据客户的浏览行为和购买行为进行细分？",

    # 营销类
    "如何根据客户对不同营销渠道的响应进行细分？",
    "如何根据客户对电子邮件营销的响应进行细分？",
    "如何根据客户对短信营销的响应进行细分？",
    "如何根据客户对社交媒体营销的响应进行细分？",
    "如何根据客户对线下活动的参与情况进行细分？",
    "如何根据客户对线上广告的点击情况进行细分？",
    "如何根据客户对会员活动的参与情况进行细分？",
    "如何根据客户对会员权益的使用情况进行细分？",
    "如何根据客户对优惠券的使用情况进行细分？",
    "如何根据客户对赠品的领取情况进行细分？",

    # 服务类
    "如何根据客户对售后服务的满意度进行细分？",
    "如何根据客户对客服沟通的满意度进行细分？",
    "如何根据客户对物流配送的满意度进行细分？",
    "如何根据客户对退换货服务的满意度进行细分？",
    "如何根据客户对投诉处理的满意度进行细分？",
    "如何根据客户对服务响应速度的满意度进行细分？",
    "如何根据客户对服务态度的满意度进行细分？",
    "如何根据客户对服务内容的满意度进行细分？",
    "如何根据客户对服务渠道的偏好进行细分？",
    "如何根据客户对服务方式的偏好进行细分？"
]


class Visitor_Robot_Connect:
    def vistor_enter(self, question=None, transfer_trigger=1):
        visitor_rest = V6_CusInfoInit().send_request().json()
        uid = visitor_rest['uid']
        cid = visitor_rest['cid']
        aiAgentCid = visitor_rest['aiAgentCid']
        print(f"uid的值为：{uid},cid的值为：{cid}，aiAgentCid的值为：{aiAgentCid}")
        if question is None:
            question = random.choice(question_list)
        if transfer_trigger == 1:
            print(f"不触发转人工")
            response = ask_AI_question(cid=cid, uid=uid, question=question, showQuestion=question, robotId="21",
                                       aiAgentCid=aiAgentCid).send_request()
        else:
            print(f"触发转人工")
            for i in range(2):
                response = ask_AI_question(cid=cid, uid=uid, question=question, showQuestion=question, robotId="21",
                                           aiAgentCid=aiAgentCid).send_request()
        print(f"Response status code: {response.status_code}")
        print(f"Response content: {response.text}")

        if response.status_code == 200:
            try:
                result_resp = response.json()
                print(f"Response JSON: {result_resp}")
            except JSONDecodeError as e:
                print(f"JSONDecodeError: {e}")
                result_resp = None
        else:
            print(f"Request failed with status code: {response.status_code}")
            result_resp = None
        resp = VisitorOffline(uid=uid).send_request().json()
        print(f"访客离线resp的值为：{resp}")
        return result_resp

    def loop_connect(self, loop_num=1):
        count_num = 0
        transfer_trigger_count = 0

        try:
            for i in range(0,loop_num):
                transfer_trigger = i % 2
                self.vistor_enter(transfer_trigger=transfer_trigger)
                count_num += 1
                if transfer_trigger == 0:
                    transfer_trigger_count += 1
        except Exception as e:
            print(f"发生异常：{e}")
        finally:
            print(f"循环次数为：{count_num}")
            print(f"触发人工次数为：{transfer_trigger_count}")
