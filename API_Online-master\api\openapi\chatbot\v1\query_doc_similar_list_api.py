# -*- coding: UTF-8 -*

from api.openapi.online_base_api import OnlineBase<PERSON>penApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class QueryDocSimilarApi(OnlineBaseOpen_v1_Api):
    """
    查询相似问题
    """
    def __init__(self, docid, robot_flag, page_no, key_words= None):
        super().__init__()
        self.url = self.host + '/api/robot/5/query_doc_similar_list'
        self.method = 'post'
        self.headers['content-type'] = "application/json"


        self.json = {
            "docid": docid,   # 词条id
            "key_words": key_words,   # 搜索关键词   可以为空
            "robot_flag": robot_flag,
            "page_no": page_no
        }