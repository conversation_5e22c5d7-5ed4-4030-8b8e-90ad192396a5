import os
import allure
import pytest
import json
from pytest import assume
from ..hui_api.stars import Stars
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager

"""测试在线工作台-标星"""

@allure.epic('在线工作台-标星')
@allure.feature('在线工作台-标星')
class Test_Stars():
    @pytest.mark.order(5)  # 添加顺序标记，数字越小优先级越高
    # @allure.severity(allure.severity_level.CRITICAL)  # 设置测试用例的严重程度
    @allure.story('在线工作台-标星')
    @allure.title('在线工作台-标星成功')
    def test_stars(self):
        with allure.step("在线工作台-标星"):
        
            
            # 创建用户初始化实例
            stars = Stars()
           
            resp = stars.send_request()
        
            
            assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')


            # 断言cid成功提取
            # assume(stars.cid is not None, "cid提取失败，值为None")
            # 断言uid成功提取
            # assume(stars.uid is not None, "uid提取失败，值为None")

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件