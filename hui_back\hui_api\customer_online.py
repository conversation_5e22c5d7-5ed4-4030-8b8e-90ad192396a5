"""
在线客服工作台-在线状态-在线
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class CustomerOnline(OnlineBaseSaasApi):
    """客服在线状态API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 内联所有设置
        self.url = (config.get('user_url', config.get('url', '')) +
                   config.get('customer_online_status_path', config.get('path', '')))
        self.method, self.headers, self.data = 'post', {'content-type': 'application/json'}, None
        self.json = {
            'uid': '${uid}'  # 使用变量引用，自动替换
        }

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params('uid')
        return super().send_request(**kwargs)
