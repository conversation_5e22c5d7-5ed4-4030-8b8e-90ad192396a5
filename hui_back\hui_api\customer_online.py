"""
在线客服工作台-在线状态-在线
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class CustomerOnline(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['user_url'] + env_config['customer_online_status_path']
        else:
            self.url = env_config['url'] + env_config['customer_online_status_path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/json'
        }
        # 确保从变量管理器获取已提取的 uid
        extracted_uid = self.ensure_global_param('uid')

        # 如果提取的值是默认值，尝试从配置获取
        if str(extracted_uid).startswith('default_'):
            extracted_uid = env_config.get('uid', 'xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj+IBS1zqXJr8fZ4ny5vE8t')

        print(f"[CustomerOnline] 使用的 uid: {extracted_uid}")

        self.json = {
                'uid': extracted_uid
        }
