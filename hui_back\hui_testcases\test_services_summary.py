import os
import allure
import pytest
import json
from ..hui_api.services_summary import  ServicesSummary
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager
from hui_common.utils import parse_json_response

"""测试服务总结-无效服务总结"""

@allure.epic('无效服务总结')
@allure.feature('无效服务总结')
class Test_ServicesSummary():
    @pytest.mark.order(6)  # 添加顺序标记，数字越小优先级越高
    @allure.story('无效服务总结')
    @allure.title('无效服务总结成功')
    def test_services_summary(self):
        with allure.step("无效服务总结"):
            # 确保用户已初始化，获取 uid 和 cid
            from ..hui_api.user_init import UserInit

            # 先进行用户初始化，确保 uid 和 cid 被提取
            user_init = UserInit()
            init_resp = user_init.send_request()

            print(f"用户初始化完成，提取的 uid: {user_init.uid}")
            print(f"用户初始化完成，提取的 cid: {user_init.cid}")

            # 创建服务总结实例
            services_summary = ServicesSummary()

            resp = services_summary.send_request()

            # 解析json响应
            resp_json = parse_json_response(resp)

            # 断言响应成功
            assert resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}'
            
        

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件