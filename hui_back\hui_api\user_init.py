"""
用户初始化接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class UserInit(OnlineBaseSaasApi):
    """用户初始化API类 """

    def send_request(self, **kwargs):
        """通用方法实现：配置 + 刷新变量 + 发送请求"""
        # 获取配置并一次性完成所有设置
        config = self.configInfo

        # 配置API - 内联所有设置
        self.url = (config.get('user_url', config.get('url', '')) +
                   config.get('user_init_path', config.get('path', '')))
        self.method, self.headers, self.json = 'post', {'content-type': 'application/x-www-form-urlencoded'}, None
        self.data = {
            'source': 0,
            'lanFlag': '',
            'locale': '',
            'language': 'zh',
            'robotFlag': '',
            'channelFlag': '',
            'platformUnionCode': '',
            'faqId': '',
            'schemeId': '',
            'ruleId': '',
            'ack': 1,
            'isReComment': 1,
            'chooseAdminId': '',
            'agid': config.get('X', ''),
            'aid': '',
            'uid': config.get('X', ''),  # 使用配置中的X值作为初始uid
            'tranFlag': '',
            'groupId': '',
            'partnerId': '',
            'tel': '',
            'email': '',
            'visitUrl': '',
            'face': '',
            'weibo': '',
            'weixin': '',
            'qq': '',
            'sex': '',
            'birthday': '',
            'remark': '',
            'params': '',
            'customerFields': '',
            'visitStartTime': '',
            'multiParams': '',
            'summaryParams': '',
            'sign': '',
            'newFlag': 1,
            'flowType': '',
            'flowCompanyId': '',
            'flowGroupId': '',
            'isVip': '',
            'vipLevel': '',
            'userLabel': '',
            'xst': config.get('X', ''),
            'toTiao_clickId': '',
            'sogou_logidUrl': '',
            'isJs': 0,
            'joinType': '',
            'shopifyDomain': '',
            'shopifyShopId': '',
            'countryName': '',
            'visitTitle': '',
            'realname': '',
            'enterpriseName': '',
            'sysNum': config.get('sysNum', ''),
            'uame': '',
            'timezoneId': '',
            'cid': config.get('X', '')  # 添加cid字段
        }

        # 发送请求并获取响应
        response = super().send_request(**kwargs)

        # 提取并保存uid和cid
        if response:
            self.cid = response.extract_and_save('$.cid', 'cid')
            self.uid = response.extract_and_save('$.uid', 'uid')
            print(f"用户初始化完成，提取的 uid: {self.uid}")
            print(f"用户初始化完成，提取的 cid: {self.cid}")

        return response
