[2025-06-18 15:18:19,481] [INFO] [apitest] - ===== 开始执行自动化测试 =====
[2025-06-18 15:18:19,486] [INFO] [apitest] - 指定环境: ali
[2025-06-18 15:18:19,487] [INFO] [root] - 设置登录配置: user=<EMAIL>
[2025-06-18 15:18:19,488] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 15:18:19,488] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 15:18:19,488] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:18:19,488] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:18:19,492] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 15:18:19,492] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 15:18:19,492] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 15:18:19,493] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 15:18:19,494] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 15:18:19,494] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 15:18:19,495] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 15:18:19,495] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 15:18:19,495] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 15:18:19,495] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 15:18:19,495] [DEBUG] [root] - 额外参数: {}
[2025-06-18 15:18:19,495] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 15:18:19,495] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:18:19,496] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 15:18:19,497] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 15:18:19,497] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:18:19,497] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 15:18:19,500] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): 127.0.0.1:9999
[2025-06-18 15:18:20,067] [DEBUG] [urllib3.connectionpool] - http://127.0.0.1:9999 "POST http://api-c.sobot.com/text/basic-login/account/consoleLogin/4 HTTP/1.1" 200 None
[2025-06-18 15:18:20,068] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 15:18:20,069] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.D7N6nzY5tDP8RKZIS8bRYpEMkpnwM-SreBw3j4HIrIU","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 15:18:20,069] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 15:18:20,069] [INFO] [root] - 【API响应】请求完成，耗时: 0.57秒，状态码: 200
[2025-06-18 15:18:20,070] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 15:18:20,070] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 15:18:20,070] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 15:18:20,070] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:18:20,070] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 15:18:20,071] [INFO] [apitest] - 环境 ali 登录成功
[2025-06-18 15:18:20,072] [INFO] [apitest] - #######################################################################
[2025-06-18 15:18:20,072] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:18:20,072] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:18:20,073] [INFO] [apitest] - 请求参数 json：{'sysNum': 'bdbe5660b2eb45758739527fda974091'}
[2025-06-18 15:18:20,076] [DEBUG] [urllib3.connectionpool] - Starting new HTTP connection (1): 127.0.0.1:9999
[2025-06-18 15:18:20,863] [DEBUG] [urllib3.connectionpool] - http://127.0.0.1:9999 "POST http://api-c.soboten.com/text/chat-visit/user/init/v6 HTTP/1.1" 200 0
[2025-06-18 15:18:20,863] [INFO] [apitest] - 响应码：200
[2025-06-18 15:18:20,864] [INFO] [apitest] - 响应体：
[2025-06-18 15:18:20,864] [INFO] [apitest] - #######################################################################


[2025-06-18 15:18:20,865] [INFO] [apitest] - 环境 ali 用户初始化成功，返回空响应
[2025-06-18 15:18:20,866] [INFO] [apitest] - 开始执行ali 环境下的测试用例
[2025-06-18 15:18:20,866] [INFO] [apitest] - 执行测试用例
