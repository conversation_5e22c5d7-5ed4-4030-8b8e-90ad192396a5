[2025-06-18 15:33:02,351] [INFO] [pytest_result_log] - Start: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init
[2025-06-18 15:33:02,609] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-18 15:33:02,609] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-18 15:33:02,610] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-18 15:33:02,611] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-18 15:33:02,611] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 15:33:02,612] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 15:33:02,612] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:33:02,612] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 15:33:02,620] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 15:33:02,620] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 15:33:02,621] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 15:33:02,621] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 15:33:02,621] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 15:33:02,621] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 15:33:02,622] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 15:33:02,622] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 15:33:02,622] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 15:33:02,622] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 15:33:02,622] [DEBUG] [root] - 额外参数: {}
[2025-06-18 15:33:02,622] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 15:33:02,623] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:33:02,623] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 15:33:02,623] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 15:33:02,623] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 15:33:02,623] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 15:33:03,078] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 15:33:03,078] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.5nVTCX-Ajerfr6weODfeGDJHeOln3GKIr__GsOx25eo","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 15:33:03,078] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 15:33:03,078] [INFO] [root] - 【API响应】请求完成，耗时: 0.46秒，状态码: 200
[2025-06-18 15:33:03,079] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 15:33:03,079] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 15:33:03,080] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 15:33:03,080] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:33:03,080] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 15:33:03,080] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:33:03,081] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-18 15:33:03,091] [INFO] [apitest] - #######################################################################
[2025-06-18 15:33:03,092] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:33:03,092] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-kwb/admin/online.action
[2025-06-18 15:33:03,092] [INFO] [apitest] - 请求参数 json：{'uid': 'xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj+IBS1zqXJr8fZ4ny5vE8t'}
[2025-06-18 15:33:04,083] [INFO] [apitest] - 响应码：200
[2025-06-18 15:33:04,084] [INFO] [apitest] - 响应体：
[2025-06-18 15:33:04,084] [INFO] [apitest] - #######################################################################


[2025-06-18 15:33:04,086] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init): 
[2025-06-18 15:33:04,087] [INFO] [pytest_result_log] - End: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init-
[2025-06-18 15:33:04,089] [INFO] [pytest_result_log] - Start: hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest
[2025-06-18 15:33:04,090] [INFO] [hui_back.hui_testcases.test_login] - 成功获取会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 15:33:04,090] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest): 
[2025-06-18 15:33:04,092] [INFO] [pytest_result_log] - End: hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest
[2025-06-18 15:33:04,094] [INFO] [pytest_result_log] - Start: hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary
[2025-06-18 15:33:04,097] [INFO] [apitest] - #######################################################################
[2025-06-18 15:33:04,101] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:33:04,103] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:33:04,105] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750231977_93f3efc9&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750231977_93f3efc9
[2025-06-18 15:35:38,086] [INFO] [apitest] - 响应码：502
[2025-06-18 15:35:38,086] [INFO] [apitest] - 响应体：
[2025-06-18 15:35:38,086] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:38,087] [WARNING] [apitest] - 响应内容为空，无法进行提取
[2025-06-18 15:35:38,088] [INFO] [apitest] - 通过路径 $.cid 没有找到cid，尝试使用extract_cid方法搜索更多路径
[2025-06-18 15:35:38,088] [WARNING] [apitest] - 响应内容为空，无法提取cid
[2025-06-18 15:35:38,088] [WARNING] [apitest] - 无法从路径【$.cid】提取到值，变量【cid】未保存
[2025-06-18 15:35:38,088] [WARNING] [apitest] - 响应内容为空，无法进行提取
[2025-06-18 15:35:38,090] [WARNING] [apitest] - 响应内容为空，无法进行提取
[2025-06-18 15:35:38,090] [WARNING] [apitest] - 无法从路径【$.uid】提取到值，变量【uid】未保存
[2025-06-18 15:35:38,611] [ERROR] [pytest_result_log] - test status is FAILED (hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary): ValueError
[2025-06-18 15:35:38,611] [DEBUG] [pytest_result_log] - hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary -> self = <hui_back.hui_testcases.test_services_summary.Test_ServicesSummary object at 0x00000201C1048AD0>

    @pytest.mark.order(6)  # 添加顺序标记，数字越小优先级越高
    @allure.story('无效服务总结')
    @allure.title('无效服务总结成功')
    def test_services_summary(self):
        with allure.step("无效服务总结"):
            # 确保用户已初始化，获取 uid 和 cid
            from ..hui_api.user_init import UserInit
    
            # 先进行用户初始化，确保 uid 和 cid 被提取
>           user_init = UserInit()

hui_testcases\test_services_summary.py:24: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
hui_api\user_init.py:102: in __init__
    self.validate_and_update(uid=self.uid, cid=self.cid)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <hui_back.hui_api.user_init.UserInit object at 0x00000201C14AAD50>, kwargs = {'cid': None, 'uid': None}, save_to_config = True, updated_params = {'cid': None, 'uid': None}, param_name = 'cid'
param_value = None, missing_params = ['uid', 'cid']

    def validate_and_update(self, **kwargs):
        """
        验证并更新参数值，支持任意参数
        :param kwargs: 要验证和更新的参数，格式为 param_name=value
        :param save_to_config: 是否保存到配置中，默认为True
        :raises ValueError: 当必需参数未设置时抛出异常
        :return: 更新后的参数字典
        """
        # 提取 save_to_config 参数
        save_to_config = kwargs.pop('save_to_config', True)
    
        # 存储更新后的参数
        updated_params = {}
    
        # 处理每个参数
        for param_name, param_value in kwargs.items():
            # 如果传入的值不是None，使用传入的值，否则使用实例属性
            if param_value is not None:
                setattr(self, param_name, param_value)
                updated_params[param_name] = param_value
            else:
                # 尝试从实例属性获取值
                if hasattr(self, param_name):
                    updated_params[param_name] = getattr(self, param_name)
                else:
                    updated_params[param_name] = None
    
        # 验证所有参数是否存在
        missing_params = [name for name, value in updated_params.items() if not value]
        if missing_params:
>           raise ValueError(f"以下参数未设置: {', '.join(missing_params)}")
E           ValueError: 以下参数未设置: uid, cid

hui_common\base_api.py:216: ValueError
[2025-06-18 15:35:38,612] [INFO] [pytest_result_log] - End: hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary
[2025-06-18 15:35:38,613] [INFO] [pytest_result_log] - -----------Start: hui_testcases/test_stars.py::Test_Stars::test_stars-----------
[2025-06-18 15:35:38,614] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:38,614] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:38,614] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-kwb/admin/add_marklist.action
[2025-06-18 15:35:38,614] [INFO] [apitest] - 请求参数 data：sender=xOh8P5f7pLDPHW5kyWJscuzHfKg8v3Wj5TWF2jP6WSj%2BIBS1zqXJr8fZ4ny5vE8t&receiver=
[2025-06-18 15:35:40,301] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:40,303] [INFO] [apitest] - 响应体：
[2025-06-18 15:35:40,304] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:40,306] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_stars.py::Test_Stars::test_stars): 
[2025-06-18 15:35:40,308] [INFO] [pytest_result_log] - ------------End: hui_testcases/test_stars.py::Test_Stars::test_stars------------
[2025-06-18 15:35:40,311] [INFO] [pytest_result_log] - --Start: hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human---
[2025-06-18 15:35:40,313] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:40,313] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:40,314] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:35:40,315] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=default_1750231977_93f3efc9&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=default_1750231977_93f3efc9
[2025-06-18 15:35:41,531] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:41,531] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"e003e7fa3b8c4b93bdcdabe196371c12:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"10436f73bf1b46de9ef5fb5c0285075c","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:35:41,534] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:41,534] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:41,535] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:35:41,536] [INFO] [apitest] - 通过【$.cid】提取到的结果是:cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:41,537] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:41,537] [INFO] [apitest] - 将提取的值【cbebe889a9dd4f6db9f7e2110317f6d2】保存为变量【cid】
[2025-06-18 15:35:41,537] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:41,538] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:35:41,540] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-18 15:35:41,540] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:41,540] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-18 15:35:41,542] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:41,542] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:41,542] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:35:41,543] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:42,714] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:42,714] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"f903b3ff8fd74f91928f038d337c7b63:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"10436f73bf1b46de9ef5fb5c0285075c","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:35:42,716] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:42,717] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:42,717] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:42,718] [INFO] [apitest] - 请求地址：https://api-c.soboten.com/text/chat-web/user/chatconnect.action
[2025-06-18 15:35:42,718] [INFO] [apitest] - 请求参数 data：sysNum=bdbe5660b2eb45758739527fda974091&chooseAdminId=&tranFlag=0&current=false&groupId=&transferType=0&summaryParams=&transferAction=&flowType=&flowCompanyId=&flowGroupId=&activeTransfer=1&unknownQuestion=&docId=&adminHelloWord=null&uid=56de2146104f480d8418d5400c978305&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:43,928] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:43,929] [INFO] [apitest] - 响应体：{"adminHelloWord":"您好，请问有什么可以帮您的？","puid":"41f8e9de5a804f27a7798ec33c861ab8","pu":"/webchat","queueFlag":null,"aname":"api","logTraceId":"4a1310332d09455dbdccfa0626098d9d_","aface":"https://img.sobot.com/console/common/face/admin.png","wslink.bak":["wss://imwsten.sobot.com:9001/ws","wss://imwsten.sobot.com:9002/ws","wss://imwsten.sobot.com:9003/ws","wss://imwsten.sobot.com:9004/ws","wss://imwsten.sobot.com:9005/ws","wss://imwsten.sobot.com:9006/ws"],"serviceEndPushMsg":"55","aid":"83d4039186d8452b88a59873bfcf2084","wslink.default":"wss://imwsten.sobot.com:9004/ws","status":1}
[2025-06-18 15:35:43,930] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:43,931] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human): 
[2025-06-18 15:35:43,932] [INFO] [pytest_result_log] - ---End: hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human----
[2025-06-18 15:35:43,934] [INFO] [pytest_result_log] - ------Start: hui_testcases/test_user_init.py::TestUserInit::test_user_init------
[2025-06-18 15:35:43,935] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:43,935] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:43,936] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:35:43,936] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:45,284] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:45,284] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"b753e1890d6841e1a7a13b6651492b6b:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:35:45,286] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:45,287] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:45,288] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:35:45,289] [INFO] [apitest] - 通过【$.cid】提取到的结果是:cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:45,289] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:45,290] [INFO] [apitest] - 将提取的值【cbebe889a9dd4f6db9f7e2110317f6d2】保存为变量【cid】
[2025-06-18 15:35:45,290] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:45,292] [INFO] [apitest] - 完整响应内容: {
  "robotSwitchFlag": 0,
  "topBarCompanyLogoUrl": "",
  "msgToTicketFlag": 1,
  "companyName": "liuyh",
  "realuateTransferFlag": 0,
  "inputTime": 5000,
  "customDoc": "描述您的问题...",
  "waitDoc": "请稍等，您正在排队中",
  "createServiceId": null,
  "type": 3,
  "enclosureShowFlag": 0,
  "h5ExtModelManList": [
    {
      "id": "071f733bf7624d6ea3c17482c89aa4f5",
      "configId": "1678318845590052864",
      "extModelType": 1,
      "extModelSource": 4,
      "extModelName": "留言",
      "popUpsTitle": null,
      "popUpsIframeUrl": null,
      "popUpsIframeKeyFlag": 0,
      "partnerId": null,
      "extModelLink": null,
      "extModelPhoto": "https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png",
      "companyId": "bdbe5660b2eb45758739527fda974091",
      "sortNo": 1,
      "receptionType": null
    },
    {
      "id": "4273cf7354ff4dfca6a132c7e2fc80bb",
      "configId": "1678318845590052864",
      "extModelType": 2,
      "extModelSource": 4,
      "extModelName": "服务评价",
 ...
[2025-06-18 15:35:45,293] [INFO] [apitest] - 通过【$.uid】提取到的结果是:56de2146104f480d8418d5400c978305
[2025-06-18 15:35:45,294] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:45,294] [INFO] [apitest] - 将提取的值【56de2146104f480d8418d5400c978305】保存为变量【uid】
[2025-06-18 15:35:45,295] [INFO] [apitest] - #######################################################################
[2025-06-18 15:35:45,296] [INFO] [apitest] - 请求方法：post
[2025-06-18 15:35:45,296] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-visit/user/init/v6
[2025-06-18 15:35:45,296] [INFO] [apitest] - 请求参数 data：source=0&lanFlag=&locale=&language=zh&robotFlag=&channelFlag=&platformUnionCode=&faqId=&schemeId=&ruleId=&ack=1&isReComment=1&chooseAdminId=&agid=56de2146104f480d8418d5400c978305&aid=&uid=56de2146104f480d8418d5400c978305&tranFlag=&groupId=&partnerId=&tel=&email=&visitUrl=&face=&weibo=&weixin=&qq=&sex=&birthday=&remark=&params=&customerFields=&visitStartTime=&multiParams=&summaryParams=&sign=&newFlag=1&flowType=&flowCompanyId=&flowGroupId=&isVip=&vipLevel=&userLabel=&xst=56de2146104f480d8418d5400c978305&toTiao_clickId=&sogou_logidUrl=&isJs=0&joinType=&shopifyDomain=&shopifyShopId=&countryName=&visitTitle=&realname=&enterpriseName=&sysNum=bdbe5660b2eb45758739527fda974091&uame=&timezoneId=&cid=cbebe889a9dd4f6db9f7e2110317f6d2
[2025-06-18 15:35:46,454] [INFO] [apitest] - 响应码：200
[2025-06-18 15:35:46,454] [INFO] [apitest] - 响应体：{"robotSwitchFlag":0,"topBarCompanyLogoUrl":"","msgToTicketFlag":1,"companyName":"liuyh","realuateTransferFlag":0,"inputTime":5000,"customDoc":"描述您的问题...","waitDoc":"请稍等，您正在排队中","createServiceId":null,"type":3,"enclosureShowFlag":0,"h5ExtModelManList":[{"id":"071f733bf7624d6ea3c17482c89aa4f5","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"4273cf7354ff4dfca6a132c7e2fc80bb","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"leaveEffectiveScope":null,"ticketShowFlag":1,"formExplain":null,"msgLeaveTxt":"时候那些还是类别.上海喜欢音乐方式客户. 用户以后就是以及选择希望学生重要.作品出现目前必须都是.控制作品系统的是出来一点. 最后以下计划关于图片以上.比较专业来自以及影响的人合作关系.","servicePromptWord":"您好，客服 #客服昵称# 为您提供服务","safety":0,"leaveAuthFlag":0,"robotLogo":"https://img.sobot.com/console/common/face/robot.png","id":null,"topBarType":1,"uname":"亚太地区0424-0778","robotName":"小智机器人","userOutWord":"不过汽车任何这种需要学习.目前公司解决参加一点管理. 电脑两个专业服务.这样一些增加来自.正在社区因为解决完全. 什么当然部门这些北京加入. 只有社会完成系列就是.销售而且有限看到选择可以规定.必须之间有关标准系统. 功能也是经济不能.学校开始数据语言加入.主题基本资源. 之间分析一次到了管理.安全两个原因这里学校用户中心.社会内容单位电话. 东西功能人员美国社会她的我们.应该虽然必须论坛.","onORoff":1,"alertFlag":0,"domainWhiteList":"https://www.123.com","sensitive":0,"chatConnectButton":0,"appExtModelManList":[{"id":"1edc3815985d4877ade510197c5ee1e3","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"678d8c03fb2342298c428c26fc194e47","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"960da06f9e714eab915ff1fc1d33413a","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b00cff4196444f8fb2dbca44f0cc85bd","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f32e2d0fefc34db8b515bdda3c99c080","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"f4a7878c5c694a98b9a423fbee3099e4","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"userTipWord":"还有电话企业阅读进行设计过程正在.谢谢人员密码参加日期. 部门投资非常图片城市.控制影响人民这样文件发布可以.社会今年一切学生. 类别过程内容法律.特别可能下载那个.论坛图片以下工具那个只有. 名称程序系统拥有对于.电子信息经验.积分深圳网络一些类别详细分析.然后提高内容联系到了. 如果应用关系网络以下.一起研究这样过程. 对于这种学生拥有操作自己.结果能够全部设备当然.","sensitiveAuthFlag":0,"isUploadFlag":1,"commentFlag":0,"leaveCustomUrl":null,"consultGuide":null,"announceClickUrl":"http://www.wanwu.net/postsmain.jsp","robotVideoWord":"为了更好的解决您的问题，请用文字描述您的问题。","showFace":1,"adminTipWord":"项目手机主题.生活主要一样学生历史. 历史不是销售新闻.谢谢一切一个一点简介.一起必须自己正在质量.没有由于时候如何结果. 用户看到我的内容.现在事情什么就是.阅读本站工作软件. 可是系列一样如此电子销售中国.帮助控制起来深圳经济那么原因. 状态开发帖子过程.名称成为语言中文等级支持.还有国内不会对于. 社区网络怎么一直位置手机运行.能力以下最新能够标准广告影响.","topBarReceptionFlag":0,"serviceOutCountRule":0,"whiteListFlag":0,"enclosureFlag":1,"manualCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","transferManualPromptWord":"对不起，未能解决您的问题，正在为您转接人工客服","adminNoneLineFlag":1,"multiFlag":0,"accountStatus":3,"uid":"56de2146104f480d8418d5400c978305","msgTmp":"您好，为了更好地解决您的问题,请告诉我们以下内容：<br>1. 您的姓名 2. 问题描述","customerQueueFlag":0,"serviceEndPushFlag":1,"customerOffPushFlag":1,"emailShowFlag":1,"invalidSessionFlag":0,"consultEffectiveScope":null,"legalFileTypeList":["exe","sys","com","bat","dll","sh","py","html","htm","js","jsp","asp","php","aspx","css","xml","svg","xhtml","mhtml","htx","htt","stm"],"isCustomSysFlag":0,"telShowFlag":0,"isClearHistoryFlag":0,"manualBtnCount":1,"readFlag":0,"updateTime":null,"isMessageFlag":1,"userId":null,"userTipTime":4,"isFeedBackFlag":1,"visitSchemeId":"1678318845590052864","msgflag":0,"sessionPhaseAndFaqIdRespVos":null,"consultName":null,"emailFlag":1,"topBarCompanyName":"","createTime":null,"realuateFlag":0,"topBarReceptionText":"","appExtModelList":[{"id":"283b67c201524a32a9cef1d720ef64b3","configId":"1678318845590052864","extModelType":9,"extModelSource":2,"extModelName":"拍摄","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_camera_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"397fae29776e40aabc3f3233d9466a8d","configId":"1678318845590052864","extModelType":8,"extModelSource":2,"extModelName":"视频 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_video_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a15c15e570e847ef8dd0478e9770a6fe","configId":"1678318845590052864","extModelType":3,"extModelSource":2,"extModelName":"上传文件","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_choose_file_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"a44dc7e80539436e937fa850e1c86597","configId":"1678318845590052864","extModelType":7,"extModelSource":2,"extModelName":"图片 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_take_picture_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"b83efeaea6d04a29979b1869e5861cc7","configId":"1678318845590052864","extModelType":2,"extModelSource":2,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"c2ceb105115340fd9b52046f1b47a1d7","configId":"1678318845590052864","extModelType":1,"extModelSource":2,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"topBarStaffPhotoFlag":1,"adminTipTime":4,"robotHelloWord":null,"adminOutFlag":1,"showStaffNick":1,"pcExtModelList":[{"id":"a94aca2a21ad451b94248cc6881dc9b4","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"cd9eb4f8fc0e4145b659d1bdb831f4d6","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"d612bafbe802435992d78bdda4153992","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"ticketStartWay":1,"logTraceId":"6a321906bb9342cab712bef5ab96832a:","announceMsg":"9","pid":"bdbe5660b2eb45758739527fda974091","language":"zh","ticketTypeId":"9","announceMsgFlag":1,"msgClickColor":"#0767FF","isFreeVersion":0,"userOutFlag":1,"pcExtModelManList":[{"id":"147154205759461a923d16c2017a49e0","configId":"1678318845590052864","extModelType":1,"extModelSource":0,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"6346eb4582944530abfedc4a5ec851bc","configId":"1678318845590052864","extModelType":2,"extModelSource":0,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"9ef44dd3321e4b1f837b69c84c9ef2fe","configId":"1678318845590052864","extModelType":4,"extModelSource":0,"extModelName":"表情 ","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/biaoqing.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"aiAgent":false,"robotHelloWordFlag":1,"realuateInfoFlag":0,"adminHelloWord":null,"consultAuthFlag":0,"leaveType":0,"msgLeaveContentTxt":"不会商品介绍开发加入积分.历史学生类别看到专业.论坛能够而且. 那个目前搜索完全只是应用阅读.喜欢系统销售一下电话学校不会.","adminHelloWordCountRule":0,"telFlag":0,"lan":0,"robotImageWord":"为了更好的解决您的问题，请用文字描述您的问题。","adminReadFlag":0,"h5ExtModelList":[{"id":"320e01de8337482bb041d13bf4111887","configId":"1678318845590052864","extModelType":2,"extModelSource":4,"extModelName":"服务评价","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_picture_satisfaction_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null},{"id":"52232480150b4b438da325a2b22372a0","configId":"1678318845590052864","extModelType":1,"extModelSource":4,"extModelName":"留言","popUpsTitle":null,"popUpsIframeUrl":null,"popUpsIframeKeyFlag":0,"partnerId":null,"extModelLink":null,"extModelPhoto":"https://img.sobot.com/console/common/res/sobot_leavemsg_normal.png","companyId":"bdbe5660b2eb45758739527fda974091","sortNo":1,"receptionType":null}],"updateServiceId":null,"isManualBtnFlag":0,"ustatus":1,"customerSensitive":0,"topBarColor":"#4ADABE,#0DAEAF","queueflag":0,"schemeName":"默认方案","servicePromptFlag":1,"msgTxt":"您好，很抱歉我们暂时无法为您提供服务，如需帮助，请留言，我们将尽快联系并解决您的问题","wurl":"","companyId":"bdbe5660b2eb45758739527fda974091","safeUrlSwitch":0,"isNewEncrypted":0,"isLeaveCustomSysFlag":0,"topBarCompanyLogoFlag":0,"guideFlag":0,"support":1,"msgAppointFlag":0,"cid":"cbebe889a9dd4f6db9f7e2110317f6d2","robotCommentTitle":"回答不完整,理解能力差,回答错误,没有解决问题","announceClickFlag":0,"color":"#4ADABE,#0DAEAF","groupflag":0,"robotDoc":"描述您的问题...","realuateStyle":0,"smartRouteInfoFlag":0,"schemeId":"1678318845707493376","topBarFlag":1,"sensitiveEffectiveScope":null,"unReadFlag":1,"transferManualPromptFlag":1,"isLock":2,"privacyAgree":1,"leaveExplain":null,"puid":"41f8e9de5a804f27a7798ec33c861ab8","isVisit":0,"adminHelloWordFlag":1,"robotFlag":1,"showTurnManualBtn":1,"robotEmojiWord":"为了更好的解决您的问题，请用文字描述您的问题。","chooseLanType":0,"announceTopFlag":1,"consultContent":null,"topBarStaffNickFlag":1,"formAuthFlag":0,"robotUnknownWord":"非常对不起哦，不知道怎么回答这个问题呢，我会努力学习的。","isNew":0,"robotGuessFlag":1,"designButton":0,"manualType":"0,0,0,0,0,","sensitiveExplain":null,"sideBarWidth":360,"topBarCompanyNameFlag":0,"ticketTypeFlag":2,"formEffectiveScope":null,"adminNonelineTitle":"抱歉，暂无人工客服在线。","sideBarStatus":0,"robotFileWord":"为了更好的解决您的问题，请用文字描述您的问题。","serviceEndPushMsg":"55","pageCloseFlag":1}
[2025-06-18 15:35:46,456] [INFO] [apitest] - #######################################################################


[2025-06-18 15:35:46,457] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_user_init.py::TestUserInit::test_user_init): 
[2025-06-18 15:35:46,458] [INFO] [pytest_result_log] - -------End: hui_testcases/test_user_init.py::TestUserInit::test_user_init-------
[2025-06-18 15:35:46,458] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
