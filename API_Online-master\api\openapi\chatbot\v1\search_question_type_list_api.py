# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class SearchQuestionTypeList(OnlineBaseOpen_v1_Api):
    """
    查询知识库分类列表
    """
    def __init__(self, parent_typeid, robot_flag, type_flag=None):
        super().__init__()
        self.url = self.host + '/api/robot/5/search_question_type_list'
        self.method = 'post'

        self.json = {
            "parent_typeid": parent_typeid,  # 父级分类 id，顶级分类 id:-1
            "robot_flag": robot_flag,        # 知识库所属机器人：0-公共知识库，1-机器人一
            "type_flag": type_flag           # 分类类型：1-单轮问题分类(默认)；2-多轮问题分类   非必传
        }
