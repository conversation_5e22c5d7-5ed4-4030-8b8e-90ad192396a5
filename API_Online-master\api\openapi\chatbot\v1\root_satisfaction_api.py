# -*- coding: UTF-8 -*-
from api.openapi.online_base_api import OnlineBaseOpenApi
from api.openapi.online_base_v1_api import OnlineBaseOpen_v1_Api


class RobotSatisfactionApi(OnlineBaseOpen_v1_Api):
    """
    机器人回答评价
    """
    def __init__(self,visitorid, cid, docid, doc_name,  robot_flag, status, msgid):
        super().__init__()
        self.url = self.host + '/api/chat/5/user/robot_feedback'
        self.method = 'post'
        self.json = {
            "cid": cid,     # 会话id
            "msgid": msgid,   # 消息 id，若机器人咨询接口的answers有值，则传answers中的msgid
            "docid": docid,   # 词条id
            "doc_name": doc_name,         # 词条名称
            "robot_flag": robot_flag,  # 机器人id
            "status": status,          # 评价状态
            "visitorid": visitorid   # 访客id  没有 visitorid,可传入 partnerid、from 代替
            }