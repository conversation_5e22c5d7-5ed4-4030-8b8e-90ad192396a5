[2025-06-18 18:52:59,403] [INFO] [pytest_result_log] - Start: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init
[2025-06-18 18:52:59,573] [INFO] [apitest] - 【测试环境】开始设置测试环境
[2025-06-18 18:52:59,575] [INFO] [apitest] - 【环境选择】使用环境: ali
[2025-06-18 18:52:59,576] [INFO] [apitest] - 【登录流程】开始登录到 ali 环境
[2025-06-18 18:52:59,577] [INFO] [root] - 【快捷登录】使用快捷函数登录到 ali 环境
[2025-06-18 18:52:59,578] [INFO] [root] - 【登录操作】尝试登录到 ali 环境
[2025-06-18 18:52:59,578] [INFO] [root] - 【环境配置】开始加载 ali 环境配置
[2025-06-18 18:52:59,579] [DEBUG] [root] - 配置文件路径: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 18:52:59,579] [DEBUG] [root] - 读取YAML文件: C:\Users\<USER>\Downloads\API_Online-master-22\hui_back\hui_config\hui_online_common.yml
[2025-06-18 18:52:59,584] [INFO] [root] - YAML配置中的环境节点: ali
[2025-06-18 18:52:59,585] [INFO] [root] - 成功加载 ali 环境配置
[2025-06-18 18:52:59,586] [INFO] [root] - 【环境节点】ali 环境信息:
[2025-06-18 18:52:59,586] [INFO] [root] -   - URL: http://api-c.sobot.com
[2025-06-18 18:52:59,586] [INFO] [root] -   - 路径: /basic-login/account/consoleLogin/4
[2025-06-18 18:52:59,587] [INFO] [root] -   - 登录用户: <EMAIL>
[2025-06-18 18:52:59,588] [DEBUG] [hui_back.hui_common.v6_console_login] - Current headers before update: {}
[2025-06-18 18:52:59,588] [INFO] [root] - 设置 content-type: application/json (域名: http://api-c.sobot.com)
[2025-06-18 18:52:59,588] [DEBUG] [hui_back.hui_common.v6_console_login] - Headers after update: {'content-type': 'application/json'}
[2025-06-18 18:52:59,588] [DEBUG] [root] - 登录参数: user=<EMAIL>, flag=1, terminalCode=2
[2025-06-18 18:52:59,588] [DEBUG] [root] - 额外参数: {}
[2025-06-18 18:52:59,589] [DEBUG] [root] - 请求数据格式: JSON
[2025-06-18 18:52:59,589] [INFO] [root] - 【API请求】发送登录请求到: http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 18:52:59,590] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################
[2025-06-18 18:52:59,590] [INFO] [hui_back.hui_common.v6_console_login] - 请求方法：post
[2025-06-18 18:52:59,591] [INFO] [hui_back.hui_common.v6_console_login] - 请求地址：http://api-c.sobot.com/text/basic-login/account/consoleLogin/4
[2025-06-18 18:52:59,591] [INFO] [hui_back.hui_common.v6_console_login] - 请求参数 json：{'loginUser': '<EMAIL>', 'loginPwd': 'bGl1eWgxMjM=', 'loginFlag': 1, 'terminalCode': 2}
[2025-06-18 18:53:00,024] [INFO] [hui_back.hui_common.v6_console_login] - 响应码：200
[2025-06-18 18:53:00,025] [INFO] [hui_back.hui_common.v6_console_login] - 响应体：{"companyId":"bdbe5660b2eb45758739527fda974091","item":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************.FihYeIji8w2qYudD2yRNF06zELExfEHAI6wJPmZHFvw","items":[],"loginCategory":"console,serviceConsole,appConsole","newConsoleFlag":1,"retCode":"000000","retMsg":"操作成功","zone":1}
[2025-06-18 18:53:00,025] [INFO] [hui_back.hui_common.v6_console_login] - #######################################################################


[2025-06-18 18:53:00,026] [INFO] [root] - 【API响应】请求完成，耗时: 0.44秒，状态码: 200
[2025-06-18 18:53:00,027] [INFO] [root] - 【响应验证】验证成功，返回码: 000000
[2025-06-18 18:53:00,027] [DEBUG] [root] - 【响应JSON】: {"companyId": "bdbe5660b2eb45758739527fda974091", "item": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJj...
[2025-06-18 18:53:00,028] [DEBUG] [root] - 在 item 字符串中找到会话信息
[2025-06-18 18:53:00,028] [INFO] [root] - 【会话信息】成功提取会话信息: eyJ0eXAiOiJKV1Q...
[2025-06-18 18:53:00,028] [INFO] [root] - 【登录结果】登录成功，获取到会话信息
[2025-06-18 18:53:00,029] [INFO] [apitest] - 【登录成功】环境: ali, 会话ID: eyJ0eXAiOiJKV1Q...
[2025-06-18 18:53:00,030] [INFO] [apitest] - 【测试准备】环境设置完成，测试前登录成功
[2025-06-18 18:53:00,041] [INFO] [apitest] - #######################################################################
[2025-06-18 18:53:00,042] [INFO] [apitest] - 请求方法：post
[2025-06-18 18:53:00,043] [INFO] [apitest] - 请求地址：http://api-c.soboten.com/text/chat-kwb/admin/online.action
[2025-06-18 18:53:00,044] [INFO] [apitest] - 请求参数 data：{'uid': 'default_1750243979_b50fd3ec', 'cid': 'default_1750243979_b50fd3ec'}
[2025-06-18 18:53:00,045] [INFO] [apitest] - 请求参数 json：{'uid': 'default_1750243979_b50fd3ec'}
[2025-06-18 18:53:00,854] [INFO] [apitest] - 响应码：200
[2025-06-18 18:53:00,855] [INFO] [apitest] - 响应体：
[2025-06-18 18:53:00,856] [INFO] [apitest] - #######################################################################


[2025-06-18 18:53:00,858] [INFO] [pytest_result_log] - test status is PASSED (hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init): 
[2025-06-18 18:53:00,859] [INFO] [pytest_result_log] - End: hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init-
[2025-06-18 18:53:00,861] [INFO] [apitest] - 【测试完成】测试会话结束，清理环境
