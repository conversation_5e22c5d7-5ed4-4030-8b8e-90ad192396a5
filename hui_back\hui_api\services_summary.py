"""
用户服务总结-无效服务总结
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class ServicesSummary(OnlineBaseSaasApi):
    """无效服务总结API类 - 优雅重构版本"""

    def __init__(self):
        super().__init__()

        # 使用简化的配置方法
        self._configure_api()

        # 设置请求数据
        self.data = self._build_request_data()

    def _configure_api(self):
        """配置API基本信息"""
        env_config = self.configInfo

        # 构建URL - 简化逻辑
        if 'services_summary_url' in env_config:
            self.url = env_config['services_summary_url'] + env_config.get('services_summary_path', '')
        else:
            self.url = env_config.get('url', '') + env_config.get('path', '')

        # 设置请求配置
        self.method = 'post'
        self.headers = {'content-type': 'application/x-www-form-urlencoded'}
        self.json = None  # 使用form-data，不使用json

    def _build_request_data(self):
        """构建请求数据 - 使用变量引用"""
        env_config = self.configInfo
        return {
            'updateServiceId': env_config.get('Y', ''),
            'uid': '${uid}',  # 变量引用，自动替换
            'cid': '${cid}',  # 变量引用，自动替换
            'invalidSession': 1
        }

    def send_request(self, **kwargs):
        """发送请求前自动刷新参数"""
        # 刷新uid和cid参数
        self.refresh_data_with_extracted_params('uid', 'cid')

        # 调用父类方法
        return super().send_request(**kwargs)

