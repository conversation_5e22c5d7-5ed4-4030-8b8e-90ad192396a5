"""
用户服务总结-无效服务总结
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class ServicesSummary(OnlineBaseSaasApi):
    """无效服务总结API类 - 极简优雅版本"""

    # 类级别常量 - 避免重复字符串
    URL_KEY = 'services_summary_url'
    PATH_KEY = 'services_summary_path'
    REQUIRED_VARS = ('uid', 'cid')

    def __init__(self):
        super().__init__()
        config = self.configInfo

        # 一次性完成所有配置 - 链式调用风格
        self._setup_request_config(config)._setup_request_data(config)

    def _setup_request_config(self, config):
        """配置请求基本信息 - 返回self支持链式调用"""
        self.url = self._build_url(config)
        self.method = 'post'
        self.headers = {'content-type': 'application/x-www-form-urlencoded'}
        self.json = None
        return self

    def _setup_request_data(self, config):
        """设置请求数据 - 返回self支持链式调用"""
        self.data = {
            'updateServiceId': config.get('Y', ''),
            'uid': '${uid}',
            'cid': '${cid}',
            'invalidSession': 1
        }
        return self

    def _build_url(self, config):
        """构建URL - 通用逻辑"""
        return (config.get(self.URL_KEY, config.get('url', '')) +
                config.get(self.PATH_KEY, config.get('path', '')))

    def send_request(self, **kwargs):
        """发送请求 - 自动刷新变量"""
        self.refresh_data_with_extracted_params(*self.REQUIRED_VARS)
        return super().send_request(**kwargs)

