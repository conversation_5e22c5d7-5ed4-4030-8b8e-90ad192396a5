"""
用户服务总结-无效服务总结
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class ServicesSummary(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['services_summary_url'] + env_config['services_summary_path']
        else:
            self.url = env_config['url'] + env_config['path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/x-www-form-urlencoded'
            # 'content-type': 'application/json'
        }

        # 移除json属性，避免混淆
        self.json = None

        # 使用变量引用，让变量管理器在发送请求时自动替换
        self.data = {
            'updateServiceId': env_config.get('Y', ''),
            'uid': '${uid}',  # 使用变量引用，自动替换
            'cid': '${cid}',  # 使用变量引用，自动替换
            'invalidSession': 1
        }

