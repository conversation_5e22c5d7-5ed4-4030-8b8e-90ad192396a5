"""
用户服务总结-无效服务总结
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class ServicesSummary(OnlineBaseSaasApi):
    """无效服务总结API类 - 通用极简模式：支持扩展的一体化设计"""

    # 类级别配置 - 支持子类覆盖
    URL_KEYS = ('services_summary_url', 'url')
    PATH_KEYS = ('services_summary_path', 'path')
    METHOD = 'post'
    CONTENT_TYPE = 'application/x-www-form-urlencoded'
    REQUIRED_VARS = ('uid', 'cid')

    def send_request(self, **kwargs):
        """通用模式：配置 + 刷新变量 + 发送请求 - 支持子类扩展"""
        config = self.configInfo

        # 通用URL构建 - 支持多种配置键
        self.url = self._build_url(config)

        # 通用请求配置 - 支持子类覆盖
        self.method = self.METHOD
        self.headers = {'content-type': self.CONTENT_TYPE}
        self.json = None if 'form-urlencoded' in self.CONTENT_TYPE else {}

        # 构建请求数据 - 调用可覆盖的方法
        self.data = self._build_request_data(config)

        # 刷新变量并发送请求
        self.refresh_data_with_extracted_params(*self.REQUIRED_VARS)
        return super().send_request(**kwargs)

    def _build_url(self, config):
        """构建URL - 支持多种配置键模式"""
        url = next((config.get(key, '') for key in self.URL_KEYS if config.get(key)), '')
        path = next((config.get(key, '') for key in self.PATH_KEYS if config.get(key)), '')
        return url + path

    def _build_request_data(self, config):
        """构建请求数据 - 子类可覆盖此方法"""
        return {
            'updateServiceId': config.get('Y', ''),
            'uid': '${uid}',
            'cid': '${cid}',
            'invalidSession': 1
        }

