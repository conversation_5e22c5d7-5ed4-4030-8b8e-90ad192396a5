#!/usr/bin/env python3
"""
测试所有修改后的API接口
验证超级极简模式的功能完整性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from hui_common.env_manager import EnvManager
from hui_api.services_summary import ServicesSummary
from hui_api.customer_online import CustomerOnline
from hui_api.stars import Stars
from hui_api.trans_human import TransHuman
from hui_api.user_init import UserInit

def test_api(api_class, api_name):
    """测试单个API接口"""
    try:
        print(f"\n🧪 测试 {api_name}...")
        api = api_class()
        response = api.send_request()
        
        if response and response.status_code == 200:
            print(f"✅ {api_name} 测试成功 - 状态码: {response.status_code}")
            return True
        else:
            print(f"❌ {api_name} 测试失败 - 状态码: {response.status_code if response else 'None'}")
            return False
    except Exception as e:
        print(f"❌ {api_name} 测试异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试所有修改后的API接口...")
    
    # 设置环境
    env_manager = EnvManager()
    env_manager.set_environment('ali')
    print("✅ 环境设置完成: ali")
    
    # 测试接口列表
    test_cases = [
        (UserInit, "UserInit - 用户初始化"),
        (ServicesSummary, "ServicesSummary - 服务总结"),
        (CustomerOnline, "CustomerOnline - 客户在线状态"),
        (Stars, "Stars - 标星"),
        (TransHuman, "TransHuman - 转人工"),
    ]
    
    results = []
    
    for api_class, api_name in test_cases:
        success = test_api(api_class, api_name)
        results.append((api_name, success))
    
    # 输出测试结果
    print("\n" + "="*60)
    print("📊 测试结果汇总:")
    print("="*60)
    
    success_count = 0
    for api_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} - {api_name}")
        if success:
            success_count += 1
    
    print(f"\n📈 总体结果: {success_count}/{len(results)} 个接口测试成功")
    
    if success_count == len(results):
        print("🎉 所有接口测试通过！超级极简模式替换成功！")
        return True
    else:
        print("⚠️  部分接口测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
