import os
import allure
import pytest
import json
from pytest import assume
from ..hui_api.trans_human import TransHuman
from ..hui_api.user_init import UserInit
from ..hui_common.base_api import OnlineBaseSaasApi
from ..hui_common.env_manager import EnvManager

"""测试用户转人工"""    

@allure.epic('用户转人工')
@allure.feature('用户转人工')
class Test_TransHuman():
    @pytest.mark.order(4)  # 添加顺序标记，数字越小优先级越高
    # @allure.severity(allure.severity_level.CRITICAL)  # 设置测试用例的严重程度
    @allure.story('用户转人工')
    @allure.title('用户转人工成功')
    def test_trans_human(self):
        with allure.step("用户转人工"):
            
            
            # 先执行用户初始化
            with allure.step("执行用户初始化"):
                user_init = UserInit()
                user_init_resp = user_init.send_request()
                print(f"用户初始化响应: {user_init_resp.text}")
                
                # 验证用户初始化是否成功
                assume(user_init_resp.status_code == 200, f'用户初始化失败，状态码: {user_init_resp.status_code}')
                assume(user_init.uid is not None, "用户初始化后uid为空")
                assume(user_init.cid is not None, "用户初始化后cid为空")
                
                # 打印配置中的uid和cid
                print(f"配置中的uid: {OnlineBaseSaasApi.configInfo.get('uid')}")
                print(f"配置中的cid: {OnlineBaseSaasApi.configInfo.get('cid')}")
            
            # 创建用户转人工实例
            with allure.step("执行用户转人工"):
                trans_human = TransHuman()
                
                # 打印请求信息
                print(f"请求URL: {trans_human.url}")
                print(f"请求方法: {trans_human.method}")
                print(f"请求头: {trans_human.headers}")
                print(f"请求数据: {trans_human.data}")
             
                resp = trans_human.send_request()
                
                # 打印响应信息
                print(f"响应状态码: {resp.status_code}")
                print(f"响应头: {resp.headers}")
                print(f"响应内容: {resp.text}")
                
                # 解析json响应-需要抽离出独立方法-待处理
                try:
                    resp_json = resp.json()
                    print(f"解析后的JSON响应: {resp_json}")
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {str(e)}")
                    print(f"响应不是有效的 JSON 格式: {resp.text}")
                    # 断言失败，并提供详细信息
                    pytest.fail(f"响应不是有效的 JSON 格式。状态码: {resp.status_code}, 响应内容: {resp.text}")
                
                assume(resp.status_code == 200, f'预期状态码不对，预期:200，实际:{resp.status_code}')

            #从参数中获取cid
            # cid = TransHuman.cid
            # print(f"提取的cid值: {cid}")
            

if __name__ == '__main__':
    pytest.main([__file__])  # 使用 __file__ 来引用当前文件