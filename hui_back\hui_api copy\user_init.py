"""
用户初始化接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class UserInit(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['user_url'] + env_config['user_init_path']
        else:
            self.url = env_config['url'] + env_config['path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/x-www-form-urlencoded'
            # 'content-type': 'application/json'
        }
    
        # 由于使用 application/x-www-form-urlencoded 内容类型，应使用 data 而不是 json
        self.data = {
                'source': 0,
                'lanFlag': '',
                'locale': '',
                'language': 'zh',
                'robotFlag': '',
                'channelFlag': '',
                'platformUnionCode': '',
                'faqId': '',
                'schemeId': '',
                'ruleId':'',
                'ack': 1,
                'isReComment': 1,
                'chooseAdminId': '',
                #通过X获取agid，没有则使用空字符串
                'agid': env_config.get('X', ''),
                'aid': '',
                'uid': env_config.get('X', ''),  # 使用配置中的X值作为初始uid
                'tranFlag': '',
                'groupId': '',
                'partnerId': '',
                'tel': '',
                'email': '',
                'visitUrl': '',
                'face': '',
                'weibo': '',
                'weixin': '',
                'qq': '',
                'sex': '',
                'birthday': '',
                'remark': '',
                'params': '',
                'customerFields': '',
                'visitStartTime': '',
                'multiParams': '',
                'summaryParams': '',
                'sign': '',
                'newFlag': 1,
                'flowType': '',
                'flowCompanyId': '',
                'flowGroupId': '',
                'isVip': '',
                'vipLevel': '',
                'userLabel': '',
                'xst': env_config.get('X', ''),
                'toTiao_clickId': '',
                'sogou_logidUrl': '',
                'isJs': 0,
                'joinType': '',
                'shopifyDomain': '',
                'shopifyShopId': '',
                'countryName': '',
                'visitTitle': '',
                'realname': '',
                'enterpriseName': '',
                'sysNum': env_config.get('sysNum', ''),
                'uame': '',
                'timezoneId': ''
        }
        # 移除json属性，避免混淆
        self.json = None
        
        # 发送请求获取响应
        response = self.send_request()
        
        #提取cid
        self.cid = response.extract_and_save('$.cid','cid')
        print(f'********** 提取的cid：{self.cid}')
        print(f'提取的cid类型：{type(self.cid)}')
        print(f'提取的cid值：{self.cid}')
        
        #提取uid
        self.uid = response.extract_and_save('$.uid','uid')
        print(f'********** 提取的uid：{self.uid}')
        print(f'提取的uid类型：{type(self.uid)}')
        print(f'提取的uid值：{self.uid}')
        
        # 使用新的验证和更新方法
        self.validate_and_update(uid=self.uid, cid=self.cid)
        
        # 打印最终的请求数据
        print(f"UserInit最终请求数据: {self.data}")

       
