["conftest.py::test_direct_login", "hui_testcases/test_customer_online.py::Test_CustomerOnline::test_user_init", "hui_testcases/test_customer_online.py::Test_KeyAlertApi::test_user_init", "hui_testcases/test_login.py::TestLoginFunctionality::test_login_from_conftest", "hui_testcases/test_login.py::TestLoginFunctionality::test_manual_login", "hui_testcases/test_services_summary.py::Test_ServicesSummary::test_services_summary", "hui_testcases/test_stars.py::Test_KeyAlertApi::test_user_init", "hui_testcases/test_stars.py::Test_Stars::test_stars", "hui_testcases/test_stars.py::Test_Stars::test_user_init", "hui_testcases/test_trans_human.py::Test_KeyAlertApi::test_user_init", "hui_testcases/test_trans_human.py::Test_TransHuman::test_trans_human", "hui_testcases/test_user_init.py::TestUserInit::test_user_init", "hui_testcases/test_user_init.py::Test_KeyAlertApi::test_user_init", "hui_testcases/test_v6_login.py::Test_KeyAlertApi::test_user_init", "tests/test_variables_manager.py::test_cache_mechanism", "tests/test_variables_manager.py::test_ensure_variable", "tests/test_variables_manager.py::test_extract_from_multiple_apis", "tests/test_variables_manager.py::test_process_dict_values", "tests/test_variables_manager.py::test_replace_variables", "tests/test_variables_manager.py::test_set_and_get_variable", "tests/test_variables_manager.py::test_variables_manager_singleton", "verify_assume_behavior.py::test_assert_behavior", "verify_assume_behavior.py::test_assume_behavior", "verify_assume_behavior.py::test_mixed_behavior"]