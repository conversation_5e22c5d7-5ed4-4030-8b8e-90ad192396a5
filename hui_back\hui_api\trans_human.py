"""
用户转人工接口
"""
from ..hui_common.base_api import OnlineBaseSaasApi


class TransHuman(OnlineBaseSaasApi):
    def __init__(self):
        super().__init__()
        # 获取yml配置
        env_config = OnlineBaseSaasApi.configInfo
        # 判断user_url 如存在，则拼接path，否使用url进行拼接
        if 'user_url' in env_config:
            self.url = env_config['trans_human_url'] + env_config['trans_human_path']
        else:
            self.url = env_config['url'] + env_config['path']
        self.method = 'post'
        self.headers = {
            'content-type': 'application/x-www-form-urlencoded'
            # 'content-type': 'application/json'
        }
    
        # 由于使用 application/x-www-form-urlencoded 内容类型，应使用 data 而不是 json
        self.data = {
                #从yml中获取sysNum
                'sysNum': env_config['sysNum'],
                'chooseAdminId': '',
                'tranFlag': '0',
                'current': 'false',
                'groupId': '',
                'transferType': '0',
                'summaryParams': '',
                'transferAction': '',
                'flowType': '',
                'flowCompanyId': '',
                'flowGroupId': '',
                'activeTransfer': '1',
                'unknownQuestion': '',
                'docId': '',
                'adminHelloWord': 'null',
        }
        # 移除json属性，避免混淆
        self.json = None
        
        # 确保从变量管理器获取已提取的 uid 和 cid
        extracted_uid = self.ensure_global_param('uid')
        extracted_cid = self.ensure_global_param('cid')

        # 如果提取的值是默认值，尝试从配置获取
        if str(extracted_uid).startswith('default_'):
            extracted_uid = env_config.get('uid', '')
        if str(extracted_cid).startswith('default_'):
            extracted_cid = env_config.get('cid', '')

        print(f"[TransHuman] 提取到的 uid: {extracted_uid}")
        print(f"[TransHuman] 提取到的 cid: {extracted_cid}")

        # 设置实例属性
        self.uid = extracted_uid
        self.cid = extracted_cid

        # 使用新的验证和更新方法
        self.validate_and_update(uid=self.uid, cid=self.cid, save_to_config=False)

        # 打印请求数据
        print(f"TransHuman请求数据: {self.data}")
        #提取cid
        # self.cid = self.response.extract_and_save('$.cid','cid')
        # print(f'********** 提取的cid：{self.cid}')
        # print(f'提取的cid类型：{type(self.cid)}')
        # print(f'提取的cid值：{self.cid}')
        ####################################################
        #从请求参数中获取cid
        # self.cid = self.data['cid']
        # print(f'********** 提取的cid：{self.cid}')
        # print(f'提取的cid类型：{type(self.cid)}')
        # print(f'提取的cid值：{self.cid}')
 
       
